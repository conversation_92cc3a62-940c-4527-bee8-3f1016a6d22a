globalThis.process??={},globalThis.process.env??={};import{c as createAstro,a as createComponent,r as renderComponent,d as renderTemplate,m as maybeRenderHead}from"../chunks/astro/server_BdgiS2eL.mjs";import{$ as $$Layout}from"../chunks/Layout_D75lx9rg.mjs";import{$ as $$ProductCard}from"../chunks/ProductCard_DdpmCjVU.mjs";import{a as $$StructuredData}from"../chunks/StructuredData_COybLFS3.mjs";import{c as createPolarClient,t as transformPolarProduct}from"../chunks/polar_D7XkB6p_.mjs";export{renderers}from"../renderers.mjs";const $$Astro=createAstro("https://infpik.store"),$$Index=createComponent((async(e,t,r)=>{const a=e.createAstro($$Astro,t,r);a.self=$$Index;let o=[],s=null,l="";const n=new URL(a.request.url);l=n.searchParams.get("search")||"";try{const e=a.locals?.runtime?.env,t=createPolarClient(e),r=e?.POLAR_ORGANIZATION_ID||"e394f3cd-5b1a-4a6c-b87c-e6bb00b17cca";if(r){const e=await t.products.list({organizationId:r,isArchived:!1}),a=e.result?.items||[];if(o=a.map(transformPolarProduct).filter((e=>null!==e)),l.trim()){const e=l.toLowerCase();o=o.filter((t=>t.name.toLowerCase().includes(e)||t.description.toLowerCase().includes(e)||t.tags&&t.tags.some((t=>t.toLowerCase().includes(e)))))}}}catch(e){console.error("Error fetching products:",e),s="Failed to load products"}const d={items:[{name:"Home",url:"http://infpik.store"},{name:"Products",url:"http://infpik.store/products"}]};return renderTemplate`${renderComponent(e,"Layout",$$Layout,{title:"Products - InfPik",description:"Browse our collection of 3D premium illustration images and artwork. High-quality digital assets for creative projects, available for instant download.",canonical:"http://infpik.store/products"},{default:async e=>renderTemplate`  ${renderComponent(e,"StructuredData",$$StructuredData,{type:"BreadcrumbList",data:d})} ${maybeRenderHead()}<div class="w-full px-4 md:px-8 py-8"> <section class="text-center mb-12"> ${l?renderTemplate`<div> <h1 class="text-4xl font-bold text-gray-900 mb-4">Search Results</h1> <p class="text-xl text-gray-600 max-w-2xl mx-auto"> ${o.length>0?`Found ${o.length} result${1===o.length?"":"s"} for "${l}"`:`No results found for "${l}"`} </p> ${0===o.length&&renderTemplate`<div class="mt-6"> <a href="/products" class="inline-flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold transition-all duration-200 hover:bg-accent-700 hover:scale-105"> <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path> </svg>
Browse All Products
</a> </div>`} </div>`:renderTemplate`<div> <h1 class="text-4xl font-bold text-gray-900 mb-4">Our Collection</h1> <p class="text-xl text-gray-600 max-w-2xl mx-auto">Discover beautiful digital images for your projects</p> </div>`} </section> ${s&&renderTemplate`<div class="bg-red-50 border border-red-200 rounded-xl p-6 mb-8"> <div class="flex items-center gap-3 text-red-800"> <svg class="w-6 h-6 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path> </svg> <div> <p class="font-semibold">⚠️ ${s}</p> <p class="text-sm">Please check your configuration and try again.</p> </div> </div> </div>`} ${!s&&0===o.length&&renderTemplate`<div class="text-center py-16"> <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center"> <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path> </svg> </div> <h2 class="text-2xl font-semibold text-gray-900 mb-2">No products available</h2> <p class="text-gray-600">Check back soon for new additions to our collection!</p> </div>`} ${!s&&o.length>0&&renderTemplate`<div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6"> ${o.map((t=>renderTemplate`${renderComponent(e,"ProductCard",$$ProductCard,{product:t})}`))} </div>`} </div> `})}`}),"D:/code/image/polar-image-store/src/pages/products/index.astro",void 0),$$file="D:/code/image/polar-image-store/src/pages/products/index.astro",$$url="/products",_page=Object.freeze(Object.defineProperty({__proto__:null,default:$$Index,file:$$file,url:$$url},Symbol.toStringTag,{value:"Module"})),page=()=>_page;export{page};