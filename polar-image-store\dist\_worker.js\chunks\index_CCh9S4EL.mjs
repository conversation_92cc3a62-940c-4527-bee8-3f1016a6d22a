globalThis.process??={},globalThis.process.env??={};import{g as getActionQueryString,a as deserializeActionResult,b as distExports,D as DEFAULT_404_ROUTE,A as ActionError,s as serializeActionResult,c as ACTION_RPC_ROUTE_PATTERN,f as ACTION_QUERY_PARAMS,u as unflatten$1,h as stringify$2}from"./astro-designed-error-pages_Bv2MRXcf.mjs";import{B as decryptString,C as createSlotValueFromString,E as isAstroComponentFactory,r as renderComponent,d as renderTemplate,n as REROUTE_DIRECTIVE_HEADER,A as AstroError,G as i18nNoLocaleFoundInPath,H as ResponseSentError,I as originPathnameSymbol,J as RewriteWithBodyUsed,K as GetStaticPathsRequired,M as InvalidGetStaticPathsReturn,O as InvalidGetStaticPathsEntry,P as GetStaticPathsExpectedParams,Q as GetStaticPathsInvalidRouteParam,S as PageNumberParamNotFound,D as DEFAULT_404_COMPONENT,T as NoMatchingStaticPathFound,U as PrerenderDynamicEndpointPathCollide,V as ReservedSlotName,W as renderSlotToString,X as renderJSX,Y as chunkToString,Z as isRenderInstruction,_ as MiddlewareNoDataOrNextCalled,$ as MiddlewareNotAResponse,a0 as SessionStorageInitError,a1 as SessionStorageSaveError,l as ROUTE_TYPE_HEADER,a2 as ForbiddenRewrite,a3 as ASTRO_VERSION,a4 as CspNotEnabled,a5 as green,a6 as LocalsReassigned,a7 as generateCspDigest,a8 as PrerenderClientAddressNotAvailable,w as clientAddressSymbol,a9 as ClientAddressNotAvailable,aa as StaticClientAddressNotAvailable,ab as AstroResponseHeadersReassigned,z as responseSentSymbol$1,ac as renderPage,ad as REWRITE_DIRECTIVE_HEADER_KEY,ae as REWRITE_DIRECTIVE_HEADER_VALUE,af as renderEndpoint}from"./astro/server_BdgiS2eL.mjs";import{a as appendForwardSlash,j as joinPaths,r as removeTrailingForwardSlash,p as prependForwardSlash,t as trimSlashes}from"./path_h5kZAkfu.mjs";const ACTION_API_CONTEXT_SYMBOL=Symbol.for("astro.actionAPIContext"),formContentTypes=["application/x-www-form-urlencoded","multipart/form-data"];function hasContentType(e,t){const r=e.split(";")[0].toLowerCase();return t.some((e=>r===e))}function hasActionPayload(e){return"_actionPayload"in e}function createGetActionResult(e){return t=>{if(hasActionPayload(e)&&t.toString()===getActionQueryString(e._actionPayload.actionName))return deserializeActionResult(e._actionPayload.actionResult)}}function createCallAction(e){return(t,r)=>{Reflect.set(e,ACTION_API_CONTEXT_SYMBOL,!0);return t.bind(e)(r)}}function shouldAppendForwardSlash(e,t){switch(e){case"always":return!0;case"never":return!1;case"ignore":switch(t){case"directory":return!0;case"preserve":case"file":return!1}}}function redirectIsExternal(e){return"string"==typeof e?e.startsWith("http://")||e.startsWith("https://"):e.destination.startsWith("http://")||e.destination.startsWith("https://")}async function renderRedirect(e){const{request:{method:t},routeData:r}=e,{redirect:s,redirectRoute:a}=r,o=a&&"object"==typeof s?s.status:"GET"===t?301:308,n={location:encodeURI(redirectRouteGenerate(e))};return s&&redirectIsExternal(s)?"string"==typeof s?Response.redirect(s,o):Response.redirect(s.destination,o):new Response(null,{status:o,headers:n})}function redirectRouteGenerate(e){const{params:t,routeData:{redirect:r,redirectRoute:s}}=e;if(void 0!==s)return s?.generate(t)||s?.pathname||"/";if("string"==typeof r){if(redirectIsExternal(r))return r;{let e=r;for(const r of Object.keys(t)){const s=t[r];e=e.replace(`[${r}]`,s).replace(`[...${r}]`,s)}return e}}return void 0===r?"/":r.destination}const SERVER_ISLAND_ROUTE="/_server-islands/[name]",SERVER_ISLAND_COMPONENT="_server-islands.astro",SERVER_ISLAND_BASE_PREFIX="_server-islands";function badRequest(e){return new Response(null,{status:400,statusText:"Bad request: "+e})}async function getRequestData(e){switch(e.method){case"GET":{const t=new URL(e.url).searchParams;if(!t.has("s")||!t.has("e")||!t.has("p"))return badRequest("Missing required query parameters.");const r=t.get("s");try{return{componentExport:t.get("e"),encryptedProps:t.get("p"),slots:JSON.parse(r)}}catch{return badRequest("Invalid slots format.")}}case"POST":try{const t=await e.text();return JSON.parse(t)}catch{return badRequest("Request format is invalid.")}default:return new Response(null,{status:405})}}function createEndpoint(e){const t=async t=>{const r=t.params;if(!r.name)return new Response(null,{status:400,statusText:"Bad request"});const s=r.name,a=await getRequestData(t.request);if(a instanceof Response)return a;const o=e.serverIslandMap?.get(s);if(!o)return new Response(null,{status:404,statusText:"Not found"});const n=await e.key,i=a.encryptedProps,c=""===i?"{}":await decryptString(n,i),l=JSON.parse(c);let u=(await o())[a.componentExport];const d={};for(const e in a.slots)d[e]=createSlotValueFromString(a.slots[e]);if(t.response.headers.set("X-Robots-Tag","noindex"),isAstroComponentFactory(u)){const e=u;u=function(...t){return e.apply(this,t)},Object.assign(u,e),u.propagation="self"}return renderTemplate`${renderComponent(t,"Component",u,l,d)}`};t.isAstroComponentFactory=!0;return{default:t,partial:!0}}function matchRoute(e,t){return t.routes.find((t=>t.pattern.test(e)||t.fallbackRoutes.some((t=>t.pattern.test(e)))))}const ROUTE404_RE=/^\/404\/?$/,ROUTE500_RE=/^\/500\/?$/;function isRoute404(e){return ROUTE404_RE.test(e)}function isRoute500(e){return ROUTE500_RE.test(e)}function isRoute404or500(e){return isRoute404(e.route)||isRoute500(e.route)}function isRouteServerIsland(e){return"_server-islands.astro"===e.component}function isRequestServerIsland(e,t=""){const r=new URL(e.url);return("/"===t?r.pathname.slice(t.length):r.pathname.slice(t.length+1)).startsWith("_server-islands")}function requestIs404Or500(e,t=""){const r=new URL(e.url).pathname.slice(t.length);return isRoute404(r)||isRoute500(r)}function isRouteExternalRedirect(e){return!("redirect"!==e.type||!e.redirect||!redirectIsExternal(e.redirect))}function requestHasLocale(e){return function(t){return pathHasLocale(t.url.pathname,e)}}function pathHasLocale(e,t){const r=e.split("/");for(const e of r)for(const r of t)if("string"==typeof r){if(normalizeTheLocale(e)===normalizeTheLocale(r))return!0}else if(e===r.path)return!0;return!1}function getPathByLocale(e,t){for(const r of t)if("string"==typeof r){if(r===e)return r}else for(const t of r.codes)if(t===e)return r.path;throw new AstroError(i18nNoLocaleFoundInPath)}function normalizeTheLocale(e){return e.replaceAll("_","-").toLowerCase()}function getAllCodes(e){const t=[];for(const r of e)"string"==typeof r?t.push(r):t.push(...r.codes);return t}function redirectToDefaultLocale({trailingSlash:e,format:t,base:r,defaultLocale:s}){return function(a,o){return shouldAppendForwardSlash(e,t)?a.redirect(`${appendForwardSlash(joinPaths(r,s))}`,o):a.redirect(`${joinPaths(r,s)}`,o)}}function notFound({base:e,locales:t,fallback:r}){return function(s,a){if("no"===a?.headers.get(REROUTE_DIRECTIVE_HEADER)&&void 0===r)return a;const o=s.url;return o.pathname===e+"/"||o.pathname===e||pathHasLocale(o.pathname,t)?void 0:a?(a.headers.set(REROUTE_DIRECTIVE_HEADER,"no"),new Response(a.body,{status:404,headers:a.headers})):new Response(null,{status:404,headers:{[REROUTE_DIRECTIVE_HEADER]:"no"}})}}function redirectToFallback({fallback:e,locales:t,defaultLocale:r,strategy:s,base:a,fallbackType:o}){return async function(n,i){if(i.status>=300&&e){const i=e?Object.keys(e):[],c=n.url.pathname.split("/").find((e=>{for(const r of t)if("string"==typeof r){if(r===e)return!0}else if(r.path===e)return!0;return!1}));if(c&&i.includes(c)){const i=getPathByLocale(e[c],t);let l;return l=i===r&&"pathname-prefix-other-locales"===s?n.url.pathname.includes(`${a}`)?n.url.pathname.replace(`/${c}`,""):n.url.pathname.replace(`/${c}`,"/"):n.url.pathname.replace(`/${c}`,`/${i}`),"rewrite"===o?await n.rewrite(l+n.url.search):n.redirect(l+n.url.search)}}return i}}function parseLocale(e){if("*"===e)return[{locale:e,qualityValue:void 0}];const t=[],r=e.split(",").map((e=>e.trim()));for(const e of r){const r=e.split(";").map((e=>e.trim())),s=r[0],a=r[1];if(r)if(a&&a.startsWith("q=")){const e=Number.parseFloat(a.slice(2));Number.isNaN(e)||e>1?t.push({locale:s,qualityValue:void 0}):t.push({locale:s,qualityValue:e})}else t.push({locale:s,qualityValue:void 0})}return t}function sortAndFilterLocales(e,t){const r=getAllCodes(t).map(normalizeTheLocale);return e.filter((e=>"*"===e.locale||r.includes(normalizeTheLocale(e.locale)))).sort(((e,t)=>e.qualityValue&&t.qualityValue?Math.sign(t.qualityValue-e.qualityValue):0))}function computePreferredLocale(e,t){const r=e.headers.get("Accept-Language");let s;if(r){const e=sortAndFilterLocales(parseLocale(r),t).at(0);if(e&&"*"!==e.locale)for(const r of t)if("string"==typeof r){if(normalizeTheLocale(r)===normalizeTheLocale(e.locale)){s=r;break}}else for(const t of r.codes)if(normalizeTheLocale(t)===normalizeTheLocale(e.locale)){s=t;break}}return s}function computePreferredLocaleList(e,t){const r=e.headers.get("Accept-Language");let s=[];if(r){const e=sortAndFilterLocales(parseLocale(r),t);if(1===e.length&&"*"===e.at(0).locale)return getAllCodes(t);if(e.length>0)for(const r of e)for(const e of t)if("string"==typeof e)normalizeTheLocale(e)===normalizeTheLocale(r.locale)&&s.push(e);else for(const t of e.codes)t===r.locale&&s.push(t)}return s}function computeCurrentLocale(e,t,r){for(const r of e.split("/"))for(const e of t)if("string"==typeof e){if(!r.includes(e))continue;if(normalizeTheLocale(e)===normalizeTheLocale(r))return e}else{if(e.path===r)return e.codes.at(0);for(const t of e.codes)if(normalizeTheLocale(t)===normalizeTheLocale(r))return t}for(const e of t)if("string"==typeof e){if(e===r)return e}else if(e.path===r)return e.codes.at(0)}const DELETED_EXPIRATION=new Date(0),DELETED_VALUE="deleted",responseSentSymbol=Symbol.for("astro.responseSent"),identity=e=>e;class AstroCookie{constructor(e){this.value=e}json(){if(void 0===this.value)throw new Error("Cannot convert undefined to an object.");return JSON.parse(this.value)}number(){return Number(this.value)}boolean(){return"false"!==this.value&&("0"!==this.value&&Boolean(this.value))}}class AstroCookies{#e;#t;#r;#s;constructor(e){this.#e=e,this.#t=null,this.#r=null,this.#s=!1}delete(e,t){const{maxAge:r,expires:s,...a}=t||{},o={expires:DELETED_EXPIRATION,...a};this.#a().set(e,["deleted",distExports.serialize(e,"deleted",o),!1])}get(e,t=void 0){if(this.#r?.has(e)){let[t,,r]=this.#r.get(e);return r?new AstroCookie(t):void 0}const r=t?.decode??decodeURIComponent,s=this.#o();if(e in s){const t=s[e];if(t)return new AstroCookie(r(t))}}has(e,t){if(this.#r?.has(e)){let[,,t]=this.#r.get(e);return t}return void 0!==this.#o()[e]}set(e,t,r){if(this.#s){const e=new Error("Astro.cookies.set() was called after the cookies had already been sent to the browser.\nThis may have happened if this method was called in an imported component.\nPlease make sure that Astro.cookies.set() is only called in the frontmatter of the main page.");e.name="Warning",console.warn(e)}let s;if("string"==typeof t)s=t;else{let e=t.toString();s=e===Object.prototype.toString.call(t)?JSON.stringify(t):e}const a={};if(r&&Object.assign(a,r),this.#a().set(e,[s,distExports.serialize(e,s,a),!0]),this.#e[responseSentSymbol])throw new AstroError({...ResponseSentError})}merge(e){const t=e.#r;if(t)for(const[e,r]of t)this.#a().set(e,r)}*headers(){if(null!=this.#r)for(const[,e]of this.#r)yield e[1]}static consume(e){return e.#s=!0,e.headers()}#o(){return this.#t||this.#n(),this.#t||(this.#t={}),this.#t}#a(){return this.#r||(this.#r=new Map),this.#r}#n(){const e=this.#e.headers.get("cookie");e&&(this.#t=distExports.parse(e,{decode:identity}))}}const astroCookiesSymbol=Symbol.for("astro.cookies");function attachCookiesToResponse(e,t){Reflect.set(e,astroCookiesSymbol,t)}function getCookiesFromResponse(e){let t=Reflect.get(e,astroCookiesSymbol);return null!=t?t:void 0}function*getSetCookiesFromResponse(e){const t=getCookiesFromResponse(e);if(!t)return[];for(const e of AstroCookies.consume(t))yield e;return[]}function createRequest({url:e,headers:t,method:r="GET",body:s,logger:a,isPrerendered:o=!1,routePattern:n,init:i}){const c=o?void 0:t instanceof Headers?t:new Headers(Object.entries(t).filter((([e])=>!e.startsWith(":"))));"string"==typeof e&&(e=new URL(e)),o&&(e.search="");const l=new Request(e,{method:r,headers:c,body:o?null:s,...i});if(o){let e=l.headers;const{value:t,writable:r,...s}=Object.getOwnPropertyDescriptor(l,"headers")||{};Object.defineProperty(l,"headers",{...s,get:()=>(a.warn(null,`\`Astro.request.headers\` was used when rendering the route \`${n}'\`. \`Astro.request.headers\` is not available on prerendered pages. If you need access to request headers, make sure that the page is server-rendered using \`export const prerender = false;\` or by setting \`output\` to \`"server"\` in your Astro config to make all your pages server-rendered by default.`),e),set(t){e=t}})}return l}function findRouteToRewrite({payload:e,routes:t,request:r,trailingSlash:s,buildFormat:a,base:o,outDir:n}){let i;i=e instanceof URL?e:e instanceof Request?new URL(e.url):new URL(e,new URL(r.url).origin);let c=i.pathname;const l=shouldAppendForwardSlash(s,a);if("/"!==o){i.pathname===o||i.pathname===removeTrailingForwardSlash(o)?c=l?"/":"":i.pathname.startsWith(o)&&(c=l?appendForwardSlash(i.pathname):removeTrailingForwardSlash(i.pathname),c=c.slice(o.length))}!c.startsWith("/")&&l&&i.pathname.endsWith("/")&&(c=prependForwardSlash(c)),"/"!==c||"/"===o||l||(c=""),"file"===a&&(c=c.replace(/\.html$/,"")),i.pathname="/"===o||""!==c&&"/"!==c||l?joinPaths(...[o,c].filter(Boolean)):removeTrailingForwardSlash(o);const u=decodeURI(c);let d;for(const e of t)if(e.pattern.test(u)){if(e.params&&0!==e.params.length&&e.distURL&&0!==e.distURL.length&&!e.distURL.find((e=>e.href.replace(n.toString(),"").replace(/(?:\/index\.html|\.html)$/,"")==trimSlashes(u))))continue;d=e;break}if(d)return{routeData:d,newUrl:i,pathname:u};{const e=t.find((e=>"/404"===e.route));return e?{routeData:e,newUrl:i,pathname:c}:{routeData:DEFAULT_404_ROUTE,newUrl:i,pathname:c}}}function copyRequest(e,t,r,s,a){if(t.bodyUsed)throw new AstroError(RewriteWithBodyUsed);return createRequest({url:e,method:t.method,body:t.body,isPrerendered:r,logger:s,headers:r?{}:t.headers,routePattern:a,init:{referrer:t.referrer,referrerPolicy:t.referrerPolicy,mode:t.mode,credentials:t.credentials,cache:t.cache,redirect:t.redirect,integrity:t.integrity,signal:t.signal,keepalive:t.keepalive,duplex:"half"}})}function setOriginPathname(e,t,r,s){t||(t="/");const a=shouldAppendForwardSlash(r,s);let o;o="/"===t?"/":a?appendForwardSlash(t):removeTrailingForwardSlash(t),Reflect.set(e,originPathnameSymbol,encodeURIComponent(o))}function getOriginPathname(e){const t=Reflect.get(e,originPathnameSymbol);return t?decodeURIComponent(t):new URL(e.url).pathname}const VALID_PARAM_TYPES=["string","number","undefined"];function validateGetStaticPathsParameter([e,t],r){if(!VALID_PARAM_TYPES.includes(typeof t))throw new AstroError({...GetStaticPathsInvalidRouteParam,message:GetStaticPathsInvalidRouteParam.message(e,t,typeof t),location:{file:r}})}function validateDynamicRouteModule(e,{ssr:t,route:r}){if((!t||r.prerender)&&!e.getStaticPaths)throw new AstroError({...GetStaticPathsRequired,location:{file:r.component}})}function validateGetStaticPathsResult(e,t,r){if(!Array.isArray(e))throw new AstroError({...InvalidGetStaticPathsReturn,message:InvalidGetStaticPathsReturn.message(typeof e),location:{file:r.component}});e.forEach((e=>{if("object"==typeof e&&Array.isArray(e)||null===e)throw new AstroError({...InvalidGetStaticPathsEntry,message:InvalidGetStaticPathsEntry.message(Array.isArray(e)?"array":typeof e)});if(void 0===e.params||null===e.params||e.params&&0===Object.keys(e.params).length)throw new AstroError({...GetStaticPathsExpectedParams,location:{file:r.component}});for(const[r,s]of Object.entries(e.params))void 0!==s&&"string"!=typeof s&&"number"!=typeof s&&t.warn("router",`getStaticPaths() returned an invalid path param: "${r}". A string, number or undefined value was expected, but got \`${JSON.stringify(s)}\`.`),"string"==typeof s&&""===s&&t.warn("router",`getStaticPaths() returned an invalid path param: "${r}". \`undefined\` expected for an optional param, but got empty string.`)}))}function stringifyParams(e,t){const r=Object.entries(e).reduce(((e,r)=>{validateGetStaticPathsParameter(r,t.component);const[s,a]=r;return void 0!==a&&(e[s]="string"==typeof a?trimSlashes(a):a.toString()),e}),{});return t.generate(r)}function generatePaginateFunction(e,t){return function(r,s={}){let{pageSize:a,params:o,props:n}=s;const i=a||10,c="page",l=o||{},u=n||{};let d;if(e.params.includes(`...${c}`))d=!1;else{if(!e.params.includes(`${c}`))throw new AstroError({...PageNumberParamNotFound,message:PageNumberParamNotFound.message(c)});d=!0}const h=Math.max(1,Math.ceil(r.length/i));return[...Array(h).keys()].map((s=>{const a=s+1,o=i===1/0?0:(a-1)*i,n=Math.min(o+i,r.length),p={...l,[c]:d||a>1?String(a):void 0},f=addRouteBase(e.generate({...p}),t),g=a===h?void 0:addRouteBase(e.generate({...p,page:String(a+1)}),t),m=1===a?void 0:addRouteBase(e.generate({...p,page:d||a-1!=1?String(a-1):void 0}),t),y=1===a?void 0:addRouteBase(e.generate({...p,page:d?"1":void 0}),t),w=a===h?void 0:addRouteBase(e.generate({...p,page:String(h)}),t);return{params:p,props:{...u,page:{data:r.slice(o,n),start:o,end:n-1,size:i,total:r.length,currentPage:a,lastPage:h,url:{current:f,next:g,prev:m,first:y,last:w}}}}}))}}function addRouteBase(e,t){let r=joinPaths(t,e);return""===r&&(r="/"),r}async function callGetStaticPaths({mod:e,route:t,routeCache:r,logger:s,ssr:a,base:o}){const n=r.get(t);if(!e)throw new Error("This is an error caused by Astro and not your code. Please file an issue.");if(n?.staticPaths)return n.staticPaths;if(validateDynamicRouteModule(e,{ssr:a,route:t}),a&&!t.prerender){const e=Object.assign([],{keyed:new Map});return r.set(t,{...n,staticPaths:e}),e}let i=[];if(!e.getStaticPaths)throw new Error("Unexpected Error.");i=await e.getStaticPaths({paginate:generatePaginateFunction(t,o)}),validateGetStaticPathsResult(i,s,t);const c=i;c.keyed=new Map;for(const e of c){const r=stringifyParams(e.params,t);c.keyed.set(r,e)}return r.set(t,{...n,staticPaths:c}),c}class RouteCache{logger;cache={};runtimeMode;constructor(e,t="production"){this.logger=e,this.runtimeMode=t}clearAll(){this.cache={}}set(e,t){const r=this.key(e);"production"===this.runtimeMode&&this.cache[r]?.staticPaths&&this.logger.warn(null,`Internal Warning: route cache overwritten. (${r})`),this.cache[r]=t}get(e){return this.cache[this.key(e)]}key(e){return`${e.route}_${e.component}`}}function findPathItemByKey(e,t,r,s){const a=stringifyParams(t,r),o=e.keyed.get(a);if(o)return o;s.debug("router",`findPathItemByKey() - Unexpected cache miss looking for ${a}`)}function routeIsRedirect(e){return"redirect"===e?.type}function routeIsFallback(e){return"fallback"===e?.type}async function getProps(e){const{logger:t,mod:r,routeData:s,routeCache:a,pathname:o,serverLike:n,base:i}=e;if(!s||s.pathname)return{};if(routeIsRedirect(s)||routeIsFallback(s)||s.component===DEFAULT_404_COMPONENT)return{};const c=await callGetStaticPaths({mod:r,route:s,routeCache:a,logger:t,ssr:n,base:i}),l=getParams(s,o),u=findPathItemByKey(c,l,s,t);if(!u&&(!n||s.prerender))throw new AstroError({...NoMatchingStaticPathFound,message:NoMatchingStaticPathFound.message(o),hint:NoMatchingStaticPathFound.hint([s.component])});r&&validatePrerenderEndpointCollision(s,r,l);return u?.props?{...u.props}:{}}function getParams(e,t){if(!e.params.length)return{};const r=e.pattern.exec(t)||e.fallbackRoutes.map((e=>e.pattern.exec(t))).find((e=>e));if(!r)return{};const s={};return e.params.forEach(((e,t)=>{e.startsWith("...")?s[e.slice(3)]=r[t+1]?r[t+1]:void 0:s[e]=r[t+1]})),s}function validatePrerenderEndpointCollision(e,t,r){if("endpoint"===e.type&&t.getStaticPaths){const t=e.segments[e.segments.length-1],s=Object.values(r),a=s[s.length-1];if(1===t.length&&t[0].dynamic&&void 0===a)throw new AstroError({...PrerenderDynamicEndpointPathCollide,message:PrerenderDynamicEndpointPathCollide.message(e.route),hint:PrerenderDynamicEndpointPathCollide.hint(e.component),location:{file:e.component}})}}function getFunctionExpression(e){if(!e)return;const t=e?.expressions?.filter((e=>!1===isRenderInstruction(e)));return 1===t?.length?t[0]:void 0}class Slots{#i;#c;#l;constructor(e,t,r){if(this.#i=e,this.#c=t,this.#l=r,t)for(const e of Object.keys(t)){if(void 0!==this[e])throw new AstroError({...ReservedSlotName,message:ReservedSlotName.message(e)});Object.defineProperty(this,e,{get:()=>!0,enumerable:!0})}}has(e){return!!this.#c&&Boolean(this.#c[e])}async render(e,t=[]){if(!this.#c||!this.has(e))return;const r=this.#i;if(Array.isArray(t)){if(t.length>0){const s=this.#c[e],a="function"==typeof s?await s(r):await s,o=getFunctionExpression(a);if(o){const e=async()=>"function"==typeof o?o(...t):o;return await renderSlotToString(r,e).then((e=>e))}if("function"==typeof a)return await renderJSX(r,a(...t)).then((e=>null!=e?String(e):e))}}else this.#l.warn(null,`Expected second parameter to be an array, received a ${typeof t}. If you're trying to pass an array as a single argument and getting unexpected results, make sure you're passing your array as a item of an array. Ex: Astro.slots.render('default', [["Hello", "World"]])`);const s=await renderSlotToString(r,this.#c[e]);return chunkToString(r,s)}}function getActionContext(e){const t=getCallerInfo(e),r=Boolean(e.locals._actionPayload);let s;return t&&"POST"===e.request.method&&!r&&(s={calledFrom:t.from,name:t.name,handler:async()=>{const r=Reflect.get(e,apiContextRoutesSymbol),s=shouldAppendForwardSlash(r.manifest.trailingSlash,r.manifest.buildFormat)?removeTrailingForwardSlash(t.name):t.name,a=await r.getAction(s);let o;try{o=await parseRequestBody(e.request)}catch(e){if(e instanceof TypeError)return{data:void 0,error:new ActionError({code:"UNSUPPORTED_MEDIA_TYPE"})};throw e}const n=["props","getActionResult","callAction","redirect"],i=Object.create(Object.getPrototypeOf(e),Object.fromEntries(Object.entries(Object.getOwnPropertyDescriptors(e)).filter((([e])=>!n.includes(e)))));Reflect.set(i,ACTION_API_CONTEXT_SYMBOL,!0);return a.bind(i)(o)}}),{action:s,setActionResult:function(t,r){e.locals._actionPayload={actionResult:r,actionName:t}},serializeActionResult:serializeActionResult,deserializeActionResult:deserializeActionResult}}function getCallerInfo(e){if(e.routePattern===ACTION_RPC_ROUTE_PATTERN)return{from:"rpc",name:e.url.pathname.replace(/^.*\/_actions\//,"")};const t=e.url.searchParams.get(ACTION_QUERY_PARAMS.actionName);return t?{from:"form",name:t}:void 0}async function parseRequestBody(e){const t=e.headers.get("content-type"),r=e.headers.get("Content-Length");if(t){if(hasContentType(t,formContentTypes))return await e.clone().formData();if(hasContentType(t,["application/json"]))return"0"===r?void 0:await e.clone().json();throw new TypeError("Unsupported content type")}}async function callMiddleware(e,t,r){let s,a=!1;let o=e(t,(async e=>(a=!0,s=r(t,e),s)));return await Promise.resolve(o).then((async e=>{if(a){if(void 0!==e){if(e instanceof Response==!1)throw new AstroError(MiddlewareNotAResponse);return e}if(s)return s;throw new AstroError(MiddlewareNotAResponse)}if(void 0===e)throw new AstroError(MiddlewareNoDataOrNextCalled);if(e instanceof Response==!1)throw new AstroError(MiddlewareNotAResponse);return e}))}const suspectProtoRx=/"(?:_|\\u0{2}5[Ff]){2}(?:p|\\u0{2}70)(?:r|\\u0{2}72)(?:o|\\u0{2}6[Ff])(?:t|\\u0{2}74)(?:o|\\u0{2}6[Ff])(?:_|\\u0{2}5[Ff]){2}"\s*:/,suspectConstructorRx=/"(?:c|\\u0063)(?:o|\\u006[Ff])(?:n|\\u006[Ee])(?:s|\\u0073)(?:t|\\u0074)(?:r|\\u0072)(?:u|\\u0075)(?:c|\\u0063)(?:t|\\u0074)(?:o|\\u006[Ff])(?:r|\\u0072)"\s*:/,JsonSigRx=/^\s*["[{]|^\s*-?\d{1,16}(\.\d{1,17})?([Ee][+-]?\d+)?\s*$/;function jsonParseTransform(e,t){if(!("__proto__"===e||"constructor"===e&&t&&"object"==typeof t&&"prototype"in t))return t;warnKeyDropped(e)}function warnKeyDropped(e){console.warn(`[destr] Dropping "${e}" key to prevent prototype pollution.`)}function destr(e,t={}){if("string"!=typeof e)return e;if('"'===e[0]&&'"'===e[e.length-1]&&-1===e.indexOf("\\"))return e.slice(1,-1);const r=e.trim();if(r.length<=9)switch(r.toLowerCase()){case"true":return!0;case"false":return!1;case"undefined":return;case"null":return null;case"nan":return Number.NaN;case"infinity":return Number.POSITIVE_INFINITY;case"-infinity":return Number.NEGATIVE_INFINITY}if(!JsonSigRx.test(e)){if(t.strict)throw new SyntaxError("[destr] Invalid JSON");return e}try{if(suspectProtoRx.test(e)||suspectConstructorRx.test(e)){if(t.strict)throw new Error("[destr] Possible prototype pollution");return JSON.parse(e,jsonParseTransform)}return JSON.parse(e)}catch(r){if(t.strict)throw r;return e}}function wrapToPromise(e){return e&&"function"==typeof e.then?e:Promise.resolve(e)}function asyncCall(e,...t){try{return wrapToPromise(e(...t))}catch(e){return Promise.reject(e)}}function isPrimitive(e){const t=typeof e;return null===e||"object"!==t&&"function"!==t}function isPureObject(e){const t=Object.getPrototypeOf(e);return!t||t.isPrototypeOf(Object)}function stringify$1(e){if(isPrimitive(e))return String(e);if(isPureObject(e)||Array.isArray(e))return JSON.stringify(e);if("function"==typeof e.toJSON)return stringify$1(e.toJSON());throw new Error("[unstorage] Cannot stringify value!")}const BASE64_PREFIX="base64:";function serializeRaw(e){return"string"==typeof e?e:"base64:"+base64Encode(e)}function deserializeRaw(e){return"string"!=typeof e?e:e.startsWith("base64:")?base64Decode(e.slice(7)):e}function base64Decode(e){return globalThis.Buffer?Buffer.from(e,"base64"):Uint8Array.from(globalThis.atob(e),(e=>e.codePointAt(0)))}function base64Encode(e){return globalThis.Buffer?Buffer.from(e).toString("base64"):globalThis.btoa(String.fromCodePoint(...e))}function normalizeKey(e){return e&&e.split("?")[0]?.replace(/[/\\]/g,":").replace(/:+/g,":").replace(/^:|:$/g,"")||""}function joinKeys(...e){return normalizeKey(e.join(":"))}function normalizeBaseKey(e){return(e=normalizeKey(e))?e+":":""}function filterKeyByDepth(e,t){if(void 0===t)return!0;let r=0,s=e.indexOf(":");for(;s>-1;)r++,s=e.indexOf(":",s+1);return r<=t}function filterKeyByBase(e,t){return t?e.startsWith(t)&&"$"!==e[e.length-1]:"$"!==e[e.length-1]}function defineDriver(e){return e}const DRIVER_NAME="memory",memory=defineDriver((()=>{const e=new Map;return{name:"memory",getInstance:()=>e,hasItem:t=>e.has(t),getItem:t=>e.get(t)??null,getItemRaw:t=>e.get(t)??null,setItem(t,r){e.set(t,r)},setItemRaw(t,r){e.set(t,r)},removeItem(t){e.delete(t)},getKeys:()=>[...e.keys()],clear(){e.clear()},dispose(){e.clear()}}}));function createStorage(e={}){const t={mounts:{"":e.driver||memory()},mountpoints:[""],watching:!1,watchListeners:[],unwatch:{}},r=e=>{for(const r of t.mountpoints)if(e.startsWith(r))return{base:r,relativeKey:e.slice(r.length),driver:t.mounts[r]};return{base:"",relativeKey:e,driver:t.mounts[""]}},s=(e,r)=>t.mountpoints.filter((t=>t.startsWith(e)||r&&e.startsWith(t))).map((r=>({relativeBase:e.length>r.length?e.slice(r.length):void 0,mountpoint:r,driver:t.mounts[r]}))),a=(e,r)=>{if(t.watching){r=normalizeKey(r);for(const s of t.watchListeners)s(e,r)}},o=async()=>{if(t.watching){for(const e in t.unwatch)await t.unwatch[e]();t.unwatch={},t.watching=!1}},n=(e,t,s)=>{const a=new Map,o=e=>{let t=a.get(e.base);return t||(t={driver:e.driver,base:e.base,items:[]},a.set(e.base,t)),t};for(const s of e){const e="string"==typeof s,a=normalizeKey(e?s:s.key),n=e?void 0:s.value,i=e||!s.options?t:{...t,...s.options},c=r(a);o(c).items.push({key:a,value:n,relativeKey:c.relativeKey,options:i})}return Promise.all([...a.values()].map((e=>s(e)))).then((e=>e.flat()))},i={hasItem(e,t={}){e=normalizeKey(e);const{relativeKey:s,driver:a}=r(e);return asyncCall(a.hasItem,s,t)},getItem(e,t={}){e=normalizeKey(e);const{relativeKey:s,driver:a}=r(e);return asyncCall(a.getItem,s,t).then((e=>destr(e)))},getItems:(e,t={})=>n(e,t,(e=>e.driver.getItems?asyncCall(e.driver.getItems,e.items.map((e=>({key:e.relativeKey,options:e.options}))),t).then((t=>t.map((t=>({key:joinKeys(e.base,t.key),value:destr(t.value)}))))):Promise.all(e.items.map((t=>asyncCall(e.driver.getItem,t.relativeKey,t.options).then((e=>({key:t.key,value:destr(e)})))))))),getItemRaw(e,t={}){e=normalizeKey(e);const{relativeKey:s,driver:a}=r(e);return a.getItemRaw?asyncCall(a.getItemRaw,s,t):asyncCall(a.getItem,s,t).then((e=>deserializeRaw(e)))},async setItem(e,t,s={}){if(void 0===t)return i.removeItem(e);e=normalizeKey(e);const{relativeKey:o,driver:n}=r(e);n.setItem&&(await asyncCall(n.setItem,o,stringify$1(t),s),n.watch||a("update",e))},async setItems(e,t){await n(e,t,(async e=>{if(e.driver.setItems)return asyncCall(e.driver.setItems,e.items.map((e=>({key:e.relativeKey,value:stringify$1(e.value),options:e.options}))),t);e.driver.setItem&&await Promise.all(e.items.map((t=>asyncCall(e.driver.setItem,t.relativeKey,stringify$1(t.value),t.options))))}))},async setItemRaw(e,t,s={}){if(void 0===t)return i.removeItem(e,s);e=normalizeKey(e);const{relativeKey:o,driver:n}=r(e);if(n.setItemRaw)await asyncCall(n.setItemRaw,o,t,s);else{if(!n.setItem)return;await asyncCall(n.setItem,o,serializeRaw(t),s)}n.watch||a("update",e)},async removeItem(e,t={}){"boolean"==typeof t&&(t={removeMeta:t}),e=normalizeKey(e);const{relativeKey:s,driver:o}=r(e);o.removeItem&&(await asyncCall(o.removeItem,s,t),(t.removeMeta||t.removeMata)&&await asyncCall(o.removeItem,s+"$",t),o.watch||a("remove",e))},async getMeta(e,t={}){"boolean"==typeof t&&(t={nativeOnly:t}),e=normalizeKey(e);const{relativeKey:s,driver:a}=r(e),o=Object.create(null);if(a.getMeta&&Object.assign(o,await asyncCall(a.getMeta,s,t)),!t.nativeOnly){const e=await asyncCall(a.getItem,s+"$",t).then((e=>destr(e)));e&&"object"==typeof e&&("string"==typeof e.atime&&(e.atime=new Date(e.atime)),"string"==typeof e.mtime&&(e.mtime=new Date(e.mtime)),Object.assign(o,e))}return o},setMeta(e,t,r={}){return this.setItem(e+"$",t,r)},removeMeta(e,t={}){return this.removeItem(e+"$",t)},async getKeys(e,t={}){e=normalizeBaseKey(e);const r=s(e,!0);let a=[];const o=[];let n=!0;for(const e of r){e.driver.flags?.maxDepth||(n=!1);const r=await asyncCall(e.driver.getKeys,e.relativeBase,t);for(const t of r){const r=e.mountpoint+normalizeKey(t);a.some((e=>r.startsWith(e)))||o.push(r)}a=[e.mountpoint,...a.filter((t=>!t.startsWith(e.mountpoint)))]}const i=void 0!==t.maxDepth&&!n;return o.filter((r=>(!i||filterKeyByDepth(r,t.maxDepth))&&filterKeyByBase(r,e)))},async clear(e,t={}){e=normalizeBaseKey(e),await Promise.all(s(e,!1).map((async e=>{if(e.driver.clear)return asyncCall(e.driver.clear,e.relativeBase,t);if(e.driver.removeItem){const r=await e.driver.getKeys(e.relativeBase||"",t);return Promise.all(r.map((r=>e.driver.removeItem(r,t))))}})))},async dispose(){await Promise.all(Object.values(t.mounts).map((e=>dispose(e))))},watch:async e=>(await(async()=>{if(!t.watching){t.watching=!0;for(const e in t.mounts)t.unwatch[e]=await watch(t.mounts[e],a,e)}})(),t.watchListeners.push(e),async()=>{t.watchListeners=t.watchListeners.filter((t=>t!==e)),0===t.watchListeners.length&&await o()}),async unwatch(){t.watchListeners=[],await o()},mount(e,r){if((e=normalizeBaseKey(e))&&t.mounts[e])throw new Error(`already mounted at ${e}`);return e&&(t.mountpoints.push(e),t.mountpoints.sort(((e,t)=>t.length-e.length))),t.mounts[e]=r,t.watching&&Promise.resolve(watch(r,a,e)).then((r=>{t.unwatch[e]=r})).catch(console.error),i},async unmount(e,r=!0){(e=normalizeBaseKey(e))&&t.mounts[e]&&(t.watching&&e in t.unwatch&&(t.unwatch[e]?.(),delete t.unwatch[e]),r&&await dispose(t.mounts[e]),t.mountpoints=t.mountpoints.filter((t=>t!==e)),delete t.mounts[e])},getMount(e=""){e=normalizeKey(e)+":";const t=r(e);return{driver:t.driver,base:t.base}},getMounts(e="",t={}){e=normalizeKey(e);return s(e,t.parents).map((e=>({driver:e.driver,base:e.mountpoint})))},keys:(e,t={})=>i.getKeys(e,t),get:(e,t={})=>i.getItem(e,t),set:(e,t,r={})=>i.setItem(e,t,r),has:(e,t={})=>i.hasItem(e,t),del:(e,t={})=>i.removeItem(e,t),remove:(e,t={})=>i.removeItem(e,t)};return i}function watch(e,t,r){return e.watch?e.watch(((e,s)=>t(e,r+s))):()=>{}}async function dispose(e){"function"==typeof e.dispose&&await asyncCall(e.dispose)}const builtinDrivers={"azure-app-configuration":"unstorage/drivers/azure-app-configuration",azureAppConfiguration:"unstorage/drivers/azure-app-configuration","azure-cosmos":"unstorage/drivers/azure-cosmos",azureCosmos:"unstorage/drivers/azure-cosmos","azure-key-vault":"unstorage/drivers/azure-key-vault",azureKeyVault:"unstorage/drivers/azure-key-vault","azure-storage-blob":"unstorage/drivers/azure-storage-blob",azureStorageBlob:"unstorage/drivers/azure-storage-blob","azure-storage-table":"unstorage/drivers/azure-storage-table",azureStorageTable:"unstorage/drivers/azure-storage-table","capacitor-preferences":"unstorage/drivers/capacitor-preferences",capacitorPreferences:"unstorage/drivers/capacitor-preferences","cloudflare-kv-binding":"unstorage/drivers/cloudflare-kv-binding",cloudflareKVBinding:"unstorage/drivers/cloudflare-kv-binding","cloudflare-kv-http":"unstorage/drivers/cloudflare-kv-http",cloudflareKVHttp:"unstorage/drivers/cloudflare-kv-http","cloudflare-r2-binding":"unstorage/drivers/cloudflare-r2-binding",cloudflareR2Binding:"unstorage/drivers/cloudflare-r2-binding",db0:"unstorage/drivers/db0","deno-kv-node":"unstorage/drivers/deno-kv-node",denoKVNode:"unstorage/drivers/deno-kv-node","deno-kv":"unstorage/drivers/deno-kv",denoKV:"unstorage/drivers/deno-kv","fs-lite":"unstorage/drivers/fs-lite",fsLite:"unstorage/drivers/fs-lite",fs:"unstorage/drivers/fs",github:"unstorage/drivers/github",http:"unstorage/drivers/http",indexedb:"unstorage/drivers/indexedb",localstorage:"unstorage/drivers/localstorage","lru-cache":"unstorage/drivers/lru-cache",lruCache:"unstorage/drivers/lru-cache",memory:"unstorage/drivers/memory",mongodb:"unstorage/drivers/mongodb","netlify-blobs":"unstorage/drivers/netlify-blobs",netlifyBlobs:"unstorage/drivers/netlify-blobs",null:"unstorage/drivers/null",overlay:"unstorage/drivers/overlay",planetscale:"unstorage/drivers/planetscale",redis:"unstorage/drivers/redis",s3:"unstorage/drivers/s3","session-storage":"unstorage/drivers/session-storage",sessionStorage:"unstorage/drivers/session-storage",uploadthing:"unstorage/drivers/uploadthing",upstash:"unstorage/drivers/upstash","vercel-blob":"unstorage/drivers/vercel-blob",vercelBlob:"unstorage/drivers/vercel-blob","vercel-kv":"unstorage/drivers/vercel-kv",vercelKV:"unstorage/drivers/vercel-kv"},PERSIST_SYMBOL=Symbol(),DEFAULT_COOKIE_NAME="astro-session",VALID_COOKIE_REGEX=/^[\w-]+$/,unflatten=(e,t)=>unflatten$1(e,{URL:e=>new URL(e)}),stringify=(e,t)=>stringify$2(e,{URL:e=>e instanceof URL&&e.href});class AstroSession{#u;#d;#h;#p;#f;#g;#m;#y=new Set;#w=new Set;#v=!1;#R=!1;#E=!0;static#S=new Map;constructor(e,{cookie:t=DEFAULT_COOKIE_NAME,...r},s){const{driver:a}=r;if(!a)throw new AstroError({...SessionStorageInitError,message:SessionStorageInitError.message("No driver was defined in the session configuration and the adapter did not provide a default driver.")});let o;if(this.#u=e,"object"==typeof t){const{name:e=DEFAULT_COOKIE_NAME,...r}=t;this.#p=e,o=r}else this.#p=t||DEFAULT_COOKIE_NAME;this.#h={sameSite:"lax",secure:"production"===s,path:"/",...o,httpOnly:!0},this.#d={...r,driver:a}}async get(e){return(await this.#b()).get(e)?.data}async has(e){return(await this.#b()).has(e)}async keys(){return(await this.#b()).keys()}async values(){return[...(await this.#b()).values()].map((e=>e.data))}async entries(){return[...(await this.#b()).entries()].map((([e,t])=>[e,t.data]))}delete(e){this.#g?.delete(e),this.#E&&this.#w.add(e),this.#v=!0}set(e,t,{ttl:r}={}){if(!e)throw new AstroError({...SessionStorageSaveError,message:"The session key was not provided."});let s;try{s=unflatten(JSON.parse(stringify(t)))}catch(t){throw new AstroError({...SessionStorageSaveError,message:`The session data for ${e} could not be serialized.`,hint:"See the devalue library for all supported types: https://github.com/rich-harris/devalue"},{cause:t})}this.#R||(this.#A(),this.#R=!0),this.#g??=new Map;const a=r??this.#d.ttl,o="number"==typeof a?Date.now()+1e3*a:a;this.#g.set(e,{data:s,expires:o}),this.#v=!0}destroy(){this.#P()}async regenerate(){let e=new Map;try{e=await this.#b()}catch(e){console.error("Failed to load session data during regeneration:",e)}const t=this.#m;this.#m=crypto.randomUUID(),this.#g=e,await this.#A(),t&&this.#f&&this.#f.removeItem(t).catch((e=>{console.error("Failed to remove old session data:",e)}))}async[PERSIST_SYMBOL](){if(!this.#v&&!this.#y.size)return;const e=await this.#I();if(this.#v&&this.#g){const t=await this.#b();this.#w.forEach((e=>t.delete(e)));const r=this.#C();let s;try{s=stringify(t)}catch(e){throw new AstroError({...SessionStorageSaveError,message:SessionStorageSaveError.message("The session data could not be serialized.",this.#d.driver)},{cause:e})}await e.setItem(r,s),this.#v=!1}if(this.#y.size>0){const t=[...this.#y].map((t=>e.removeItem(t).catch((e=>{console.error(`Failed to clean up session ${t}:`,e)}))));await Promise.all(t),this.#y.clear()}}get sessionID(){return this.#m}async load(e){this.#m=e,this.#g=void 0,await this.#A(),await this.#b()}async#A(){if(!VALID_COOKIE_REGEX.test(this.#p))throw new AstroError({...SessionStorageSaveError,message:"Invalid cookie name. Cookie names can only contain letters, numbers, and dashes."});const e=this.#C();this.#u.set(this.#p,e,this.#h)}async#b(){const e=await this.#I();if(this.#g&&!this.#E)return this.#g;this.#g??=new Map;const t=await e.get(this.#C());if(!t)return this.#g;try{const e=unflatten(t);if(!(e instanceof Map))throw await this.#P(),new AstroError({...SessionStorageInitError,message:SessionStorageInitError.message("The session data was an invalid type.",this.#d.driver)});const r=Date.now();for(const[t,s]of e){const e="number"==typeof s.expires&&s.expires<r;this.#g.has(t)||this.#w.has(t)||e||this.#g.set(t,s)}return this.#E=!1,this.#g}catch(e){if(await this.#P(),e instanceof AstroError)throw e;throw new AstroError({...SessionStorageInitError,message:SessionStorageInitError.message("The session data could not be parsed.",this.#d.driver)},{cause:e})}}#P(){this.#m&&this.#y.add(this.#m),this.#p&&this.#u.delete(this.#p,this.#h),this.#m=void 0,this.#g=void 0,this.#v=!0}#C(){return this.#m??=this.#u.get(this.#p)?.value??crypto.randomUUID(),this.#m}async#I(){if(this.#f)return this.#f;if(AstroSession.#S.has(this.#d.driver))return this.#f=AstroSession.#S.get(this.#d.driver),this.#f;if("test"===this.#d.driver)return this.#f=this.#d.options.mockStorage,this.#f;"fs"!==this.#d.driver&&"fsLite"!==this.#d.driver&&"fs-lite"!==this.#d.driver||(this.#d.options??={},this.#d.driver="fs-lite",this.#d.options.base??=".astro/session");let e=null;try{if(this.#d.driverModule)e=(await this.#d.driverModule()).default;else if(this.#d.driver){const t=resolveSessionDriverName(this.#d.driver);t&&(e=(await import(t)).default)}}catch(e){if("ERR_MODULE_NOT_FOUND"===e.code)throw new AstroError({...SessionStorageInitError,message:SessionStorageInitError.message(e.message.includes("Cannot find package")?"The driver module could not be found.":e.message,this.#d.driver)},{cause:e});throw e}if(!e)throw new AstroError({...SessionStorageInitError,message:SessionStorageInitError.message("The module did not export a driver.",this.#d.driver)});try{return this.#f=createStorage({driver:e(this.#d.options)}),AstroSession.#S.set(this.#d.driver,this.#f),this.#f}catch(e){throw new AstroError({...SessionStorageInitError,message:SessionStorageInitError.message("Unknown error",this.#d.driver)},{cause:e})}}}function resolveSessionDriverName(e){if(!e)return null;try{if("fs"===e)return builtinDrivers.fsLite;if(e in builtinDrivers)return builtinDrivers[e]}catch{return null}return e}const apiContextRoutesSymbol=Symbol.for("context.routes");class RenderContext{constructor(e,t,r,s,a,o,n,i,c,l=new AstroCookies(o),u=getParams(n,a),d=new URL(o.url),h={},p=void 0,f=(e.manifest.sessionConfig?new AstroSession(l,e.manifest.sessionConfig,e.runtimeMode):void 0)){this.pipeline=e,this.locals=t,this.middleware=r,this.actions=s,this.pathname=a,this.request=o,this.routeData=n,this.status=i,this.clientAddress=c,this.cookies=l,this.params=u,this.url=d,this.props=h,this.partial=p,this.session=f}isRewriting=!1;counter=0;result=void 0;static async create({locals:e={},middleware:t,pathname:r,pipeline:s,request:a,routeData:o,clientAddress:n,status:i=200,props:c,partial:l,actions:u}){const d=await s.getMiddleware(),h=u??await s.getActions();return setOriginPathname(a,r,s.manifest.trailingSlash,s.manifest.buildFormat),new RenderContext(s,e,sequence(...s.internalMiddleware,t??d),h,r,a,o,i,n,void 0,void 0,void 0,c,l)}async render(e,t={}){const{cookies:r,middleware:s,pipeline:a}=this,{logger:o,serverLike:n,streaming:i,manifest:c}=a,l=Object.keys(this.props).length>0?this.props:await getProps({mod:e,routeData:this.routeData,routeCache:this.pipeline.routeCache,pathname:this.pathname,logger:o,serverLike:n,base:c.base}),u=this.createActionAPIContext(),d=this.createAPIContext(l,u);if(this.counter++,4===this.counter)return new Response("Loop Detected",{status:508,statusText:"Astro detected a loop where you tried to call the rewriting logic more than four times."});if(isRouteExternalRedirect(this.routeData))return renderRedirect(this);const h=await callMiddleware(s,d,(async(s,n)=>{if(n){const t=this.pathname;a.logger.debug("router","Called rewriting to:",n);const{routeData:r,componentInstance:s,pathname:o,newUrl:i}=await a.tryRewrite(n,this.request);if(!0===this.pipeline.serverLike&&!1===this.routeData.prerender&&!0===r.prerender)throw new AstroError({...ForbiddenRewrite,message:ForbiddenRewrite.message(this.pathname,o,r.component),hint:ForbiddenRewrite.hint(r.component)});this.routeData=r,e=s,n instanceof Request?this.request=n:this.request=copyRequest(i,this.request,r.prerender,this.pipeline.logger,this.routeData.route),this.isRewriting=!0,this.url=new URL(this.request.url),this.params=getParams(r,o),this.pathname=o,this.status=200,setOriginPathname(this.request,t,this.pipeline.manifest.trailingSlash,this.pipeline.manifest.buildFormat)}let c;if(!s.isPrerendered){const{action:e,setActionResult:t,serializeActionResult:r}=getActionContext(s);if("form"===e?.calledFrom){const s=await e.handler();t(e.name,r(s))}}switch(this.routeData.type){case"endpoint":c=await renderEndpoint(e,s,this.routeData.prerender,o);break;case"redirect":return renderRedirect(this);case"page":this.result=await this.createResult(e,u);try{c=await renderPage(this.result,e?.default,l,t,i,this.routeData)}catch(e){throw this.result.cancelled=!0,e}c.headers.set(ROUTE_TYPE_HEADER,"page"),"/404"!==this.routeData.route&&"/500"!==this.routeData.route||c.headers.set(REROUTE_DIRECTIVE_HEADER,"no"),this.isRewriting&&c.headers.set(REWRITE_DIRECTIVE_HEADER_KEY,REWRITE_DIRECTIVE_HEADER_VALUE);break;case"fallback":return new Response(null,{status:500,headers:{[ROUTE_TYPE_HEADER]:"fallback"}})}const d=getCookiesFromResponse(c);return d&&r.merge(d),c}));return h.headers.get(ROUTE_TYPE_HEADER)&&h.headers.delete(ROUTE_TYPE_HEADER),attachCookiesToResponse(h,r),h}createAPIContext(e,t){return Reflect.set(t,apiContextRoutesSymbol,this.pipeline),Object.assign(t,{props:e,redirect:(e,t=302)=>new Response(null,{status:t,headers:{Location:e}}),getActionResult:createGetActionResult(t.locals),callAction:createCallAction(t)})}async#D(e){this.pipeline.logger.debug("router","Calling rewrite: ",e);const t=this.pathname,{routeData:r,componentInstance:s,newUrl:a,pathname:o}=await this.pipeline.tryRewrite(e,this.request),n=r.fallbackRoutes&&r.fallbackRoutes.length>0;if(this.pipeline.serverLike&&!this.routeData.prerender&&r.prerender&&!n)throw new AstroError({...ForbiddenRewrite,message:ForbiddenRewrite.message(this.pathname,o,r.component),hint:ForbiddenRewrite.hint(r.component)});return this.routeData=r,e instanceof Request?this.request=e:this.request=copyRequest(a,this.request,r.prerender,this.pipeline.logger,this.routeData.route),this.url=new URL(this.request.url),this.cookies=new AstroCookies(this.request),this.params=getParams(r,o),this.pathname=o,this.isRewriting=!0,this.status=200,setOriginPathname(this.request,t,this.pipeline.manifest.trailingSlash,this.pipeline.manifest.buildFormat),await this.render(s)}createActionAPIContext(){const e=this,{cookies:t,params:r,pipeline:s,url:a}=this,o=`Astro v${ASTRO_VERSION}`;return{cookies:t,routePattern:this.routeData.route,isPrerendered:this.routeData.prerender,get clientAddress(){return e.getClientAddress()},get currentLocale(){return e.computeCurrentLocale()},generator:o,get locals(){return e.locals},set locals(e){throw new AstroError(LocalsReassigned)},params:r,get preferredLocale(){return e.computePreferredLocale()},get preferredLocaleList(){return e.computePreferredLocaleList()},rewrite:async e=>await this.#D(e),request:this.request,site:s.site,url:a,get originPathname(){return getOriginPathname(e.request)},get session(){if(this.isPrerendered)s.logger.warn("session",`context.session was used when rendering the route ${green(this.routePattern)}, but it is not available on prerendered routes. If you need access to sessions, make sure that the route is server-rendered using \`export const prerender = false;\` or by setting \`output\` to \`"server"\` in your Astro config to make all your routes server-rendered by default. For more information, see https://docs.astro.build/en/guides/sessions/`);else{if(e.session)return e.session;s.logger.warn("session",`context.session was used when rendering the route ${green(this.routePattern)}, but no storage configuration was provided. Either configure the storage manually or use an adapter that provides session storage. For more information, see https://docs.astro.build/en/guides/sessions/`)}},insertDirective(t){if(!s.manifest.csp)throw new AstroError(CspNotEnabled);e.result?.directives.push(t)},insertScriptResource(t){if(!s.manifest.csp)throw new AstroError(CspNotEnabled);e.result?.scriptResources.push(t)},insertStyleResource(t){if(!s.manifest.csp)throw new AstroError(CspNotEnabled);e.result?.styleResources.push(t)},insertStyleHash(t){if(!s.manifest.csp)throw new AstroError(CspNotEnabled);e.result?.styleHashes.push(t)},insertScriptHash(t){if(!1==!!s.manifest.csp)throw new AstroError(CspNotEnabled);e.result?.scriptHashes.push(t)}}}async createResult(e,t){const{cookies:r,pathname:s,pipeline:a,routeData:o,status:n}=this,{clientDirectives:i,inlinedScripts:c,compressHTML:l,manifest:u,renderers:d,resolve:h}=a,{links:p,scripts:f,styles:g}=await a.headElements(o),m=[],y=[],w=!!u.csp,v=u.csp?.algorithm??"SHA-256";if(w){for(const e of g)m.push(await generateCspDigest(e.children,v));for(const e of f)y.push(await generateCspDigest(e.children,v))}const R=await a.componentMetadata(o)??u.componentMetadata,E=new Headers({"Content-Type":"text/html"}),S="boolean"==typeof this.partial?this.partial:Boolean(e.partial),b=hasActionPayload(this.locals)?deserializeActionResult(this.locals._actionPayload.actionResult):void 0,A={status:b?.error?b?.error.status:n,statusText:b?.error?b?.error.type:"OK",get headers(){return E},set headers(e){throw new AstroError(AstroResponseHeadersReassigned)}},P={base:u.base,userAssetsBase:u.userAssetsBase,cancelled:!1,clientDirectives:i,inlinedScripts:c,componentMetadata:R,compressHTML:l,cookies:r,createAstro:(e,r,s)=>this.createAstro(P,e,r,s,t),links:p,params:this.params,partial:S,pathname:s,renderers:d,resolve:h,response:A,request:this.request,scripts:f,styles:g,actionResult:b,serverIslandNameMap:u.serverIslandNameMap??new Map,key:u.key,trailingSlash:u.trailingSlash,_metadata:{hasHydrationScript:!1,rendererSpecificHydrationScripts:new Set,hasRenderedHead:!1,renderedScripts:new Set,hasDirectives:new Set,hasRenderedServerIslandRuntime:!1,headInTree:!1,extraHead:[],extraStyleHashes:m,extraScriptHashes:y,propagators:new Set},cspDestination:u.csp?.cspDestination??(o.prerender?"meta":"header"),shouldInjectCspMetaTags:w,cspAlgorithm:v,scriptHashes:u.csp?.scriptHashes?[...u.csp.scriptHashes]:[],scriptResources:u.csp?.scriptResources?[...u.csp.scriptResources]:[],styleHashes:u.csp?.styleHashes?[...u.csp.styleHashes]:[],styleResources:u.csp?.styleResources?[...u.csp.styleResources]:[],directives:u.csp?.directives?[...u.csp.directives]:[],isStrictDynamic:u.csp?.isStrictDynamic??!1};return P}#T;createAstro(e,t,r,s,a){let o;o=this.isRewriting?this.#T=this.createAstroPagePartial(e,t,a):this.#T??=this.createAstroPagePartial(e,t,a);const n={props:r,self:null},i=Object.assign(Object.create(o),n);let c;return Object.defineProperty(i,"slots",{get:()=>(c||(c=new Slots(e,s,this.pipeline.logger)),c)}),i}createAstroPagePartial(e,t,r){const s=this,{cookies:a,locals:o,params:n,pipeline:i,url:c}=this,{response:l}=e,u=createCallAction(r);return{generator:t.generator,glob:t.glob,routePattern:this.routeData.route,isPrerendered:this.routeData.prerender,cookies:a,get session(){if(this.isPrerendered)i.logger.warn("session",`Astro.session was used when rendering the route ${green(this.routePattern)}, but it is not available on prerendered pages. If you need access to sessions, make sure that the page is server-rendered using \`export const prerender = false;\` or by setting \`output\` to \`"server"\` in your Astro config to make all your pages server-rendered by default. For more information, see https://docs.astro.build/en/guides/sessions/`);else{if(s.session)return s.session;i.logger.warn("session",`Astro.session was used when rendering the route ${green(this.routePattern)}, but no storage configuration was provided. Either configure the storage manually or use an adapter that provides session storage. For more information, see https://docs.astro.build/en/guides/sessions/`)}},get clientAddress(){return s.getClientAddress()},get currentLocale(){return s.computeCurrentLocale()},params:n,get preferredLocale(){return s.computePreferredLocale()},get preferredLocaleList(){return s.computePreferredLocaleList()},locals:o,redirect:(e,t=302)=>{if(this.request[responseSentSymbol$1])throw new AstroError({...ResponseSentError});return new Response(null,{status:t,headers:{Location:e}})},rewrite:async e=>await this.#D(e),request:this.request,response:l,site:i.site,getActionResult:createGetActionResult(o),get callAction(){return u},url:c,get originPathname(){return getOriginPathname(s.request)},insertDirective(e){if(!i.manifest.csp)throw new AstroError(CspNotEnabled);s.result?.directives.push(e)},insertScriptResource(e){if(!i.manifest.csp)throw new AstroError(CspNotEnabled);s.result?.scriptResources.push(e)},insertStyleResource(e){if(!i.manifest.csp)throw new AstroError(CspNotEnabled);s.result?.styleResources.push(e)},insertStyleHash(e){if(!i.manifest.csp)throw new AstroError(CspNotEnabled);s.result?.styleHashes.push(e)},insertScriptHash(e){if(!1==!!i.manifest.csp)throw new AstroError(CspNotEnabled);s.result?.scriptHashes.push(e)}}}getClientAddress(){const{pipeline:e,request:t,routeData:r,clientAddress:s}=this;if(r.prerender)throw new AstroError({...PrerenderClientAddressNotAvailable,message:PrerenderClientAddressNotAvailable.message(r.component)});if(s)return s;if(clientAddressSymbol in t)return Reflect.get(t,clientAddressSymbol);if(e.adapterName)throw new AstroError({...ClientAddressNotAvailable,message:ClientAddressNotAvailable.message(e.adapterName)});throw new AstroError(StaticClientAddressNotAvailable)}#L;computeCurrentLocale(){const{url:e,pipeline:{i18n:t},routeData:r}=this;if(!t)return;const{defaultLocale:s,locales:a,strategy:o}=t,n="pathname-prefix-other-locales"===o||"domains-prefix-other-locales"===o?s:void 0;if(this.#L)return this.#L;let i;if(isRouteServerIsland(r)){let e=this.request.headers.get("referer");e&&(URL.canParse(e)&&(e=new URL(e).pathname),i=computeCurrentLocale(e,a,s))}else{let t=r.pathname;if(!r.pattern.test(e.pathname))for(const s of r.fallbackRoutes)if(s.pattern.test(e.pathname)){t=s.pathname;break}t=t&&!isRoute404or500(r)?t:e.pathname,i=computeCurrentLocale(t,a,s)}return this.#L=i??n,this.#L}#k;computePreferredLocale(){const{pipeline:{i18n:e},request:t}=this;if(e)return this.#k??=computePreferredLocale(t,e.locales)}#O;computePreferredLocaleList(){const{pipeline:{i18n:e},request:t}=this;if(e)return this.#O??=computePreferredLocaleList(t,e.locales)}}function sequence(...e){const t=e.filter((e=>!!e)),r=t.length;return defineMiddleware(r?(e,s)=>{let a;return function e(o,n){const i=(0,t[o])(n,(async t=>{if(o<r-1){if(t){let e;e=t instanceof Request?t:t instanceof URL?new Request(t,n.request.clone()):new Request(new URL(t,n.url.origin),n.request.clone());const r=n.url.pathname,s=Reflect.get(n,apiContextRoutesSymbol),{routeData:o,pathname:i}=await s.tryRewrite(t,n.request);if(!0===s.serverLike&&!1===n.isPrerendered&&!0===o.prerender)throw new AstroError({...ForbiddenRewrite,message:ForbiddenRewrite.message(n.url.pathname,i,o.component),hint:ForbiddenRewrite.hint(o.component)});a=t,n.request=e,n.url=new URL(e.url),n.cookies=new AstroCookies(e),n.params=getParams(o,i),n.routePattern=o.route,setOriginPathname(n.request,r,s.manifest.trailingSlash,s.manifest.buildFormat)}return e(o+1,n)}return s(t??a)}));return i}(0,e)}:(e,t)=>t())}function defineMiddleware(e){return e}export{PERSIST_SYMBOL as P,RouteCache as R,SERVER_ISLAND_COMPONENT as S,redirectToFallback as a,redirectToDefaultLocale as b,requestHasLocale as c,defineMiddleware as d,normalizeTheLocale as e,SERVER_ISLAND_ROUTE as f,createEndpoint as g,findRouteToRewrite as h,isRequestServerIsland as i,RenderContext as j,getSetCookiesFromResponse as k,matchRoute as m,notFound as n,requestIs404Or500 as r,sequence as s};