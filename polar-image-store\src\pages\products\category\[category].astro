---
import Layout from '../../../layouts/Layout.astro';
import ProductCard from '../../../components/ProductCard.astro';
import StructuredData from '../../../components/StructuredData.astro';
import { createPolarClient, transformPolarProduct, getProductsByCategory, getCategoryDisplayName } from '../../../utils/polar';
import type { LocalProduct } from '../../../types/polar';

// Get category from URL params
const { category } = Astro.params;

if (!category) {
  return Astro.redirect('/products');
}

// Fetch products at runtime
let allProducts: LocalProduct[] = [];
let products: LocalProduct[] = [];
let error: string | null = null;

try {
  // Get runtime environment from Cloudflare context
  const env = Astro.locals?.runtime?.env;
  const polar = createPolarClient(env);
  const organizationId = env?.POLAR_ORGANIZATION_ID || import.meta.env.POLAR_ORGANIZATION_ID;

  if (!organizationId) {
    error = 'Organization ID not configured';
  } else {
    const response = await polar.products.list({
      organizationId,
      isArchived: false
    });

    const productList = response.result?.items || [];
    allProducts = productList
      .map(transformPolarProduct)
      .filter((product): product is LocalProduct => product !== null);

    // Filter products by category
    products = getProductsByCategory(allProducts, category);
  }
} catch (err) {
  console.error('Error fetching products for category:', err);
  error = 'Failed to load products';
}

const categoryDisplayName = getCategoryDisplayName(category);

// Breadcrumb data
const breadcrumbData = {
  items: [
    { name: "Home", url: import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store' },
    { name: "Products", url: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products` },
    { name: categoryDisplayName, url: `${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products/category/${category}` }
  ]
};
---

<Layout
  title={`${categoryDisplayName} - InfPik`}
  description={`Browse our collection of ${categoryDisplayName.toLowerCase()} digital images and artwork. High-quality digital assets for creative projects.`}
  canonical={`${import.meta.env.PUBLIC_SITE_URL || 'https://infpik.store'}/products/category/${category}`}
>
  <!-- Breadcrumb Structured Data -->
  <StructuredData type="BreadcrumbList" data={breadcrumbData} />
  
  <div class="w-full px-4 md:px-8 py-8">
    <section class="text-center mb-12">
      <h1 class="text-4xl font-bold text-gray-900 mb-4">{categoryDisplayName}</h1>
      <p class="text-xl text-gray-600 max-w-2xl mx-auto">
        {products.length > 0
          ? `Discover ${products.length} ${categoryDisplayName.toLowerCase()} ${products.length === 1 ? 'item' : 'items'} in our collection`
          : `No ${categoryDisplayName.toLowerCase()} items found`
        }
      </p>
    </section>


    
    {products.length === 0 ? (
      <div class="text-center py-16">
        <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
          <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        <h3 class="text-2xl font-semibold text-gray-900 mb-4">No {categoryDisplayName} Found</h3>
        <p class="text-gray-600 mb-8">We don't have any {categoryDisplayName.toLowerCase()} items yet.</p>
        <a
          href="/products"
          class="inline-flex items-center gap-2 px-6 py-3 bg-accent-600 text-white rounded-full font-semibold transition-all duration-200 hover:bg-accent-700 hover:scale-105"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          Browse All Products
        </a>
      </div>
    ) : (
      <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {products.slice(0, 16).map((product) => (
          <ProductCard product={product} />
        ))}
      </div>
    )}
  </div>
</Layout>


