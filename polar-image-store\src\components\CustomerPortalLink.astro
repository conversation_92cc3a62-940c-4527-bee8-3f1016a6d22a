---
// Component để access customer portal
export interface Props {
  variant?: 'button' | 'link' | 'card';
  showEmailForm?: boolean;
  className?: string;
}

const { 
  variant = 'button', 
  showEmailForm = true,
  className = ''
} = Astro.props;
---

<div class={`customer-portal-section ${className}`}>
  {variant === 'card' && (
    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 text-center">
      <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      </div>
      <h3 class="text-lg font-semibold text-gray-900 mb-2">Access Your Orders</h3>
      <p class="text-gray-600 mb-4">View your purchase history, downloads, and manage your account</p>
      
      <!-- Direct portal link -->
      <a 
        href="/api/portal-redirect" 
        class="inline-flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-full font-semibold transition-all duration-200 hover:bg-blue-700 hover:shadow-lg hover:-translate-y-0.5 mb-4"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Go to Customer Portal
      </a>
      
      {showEmailForm && (
        <div class="border-t border-blue-200 pt-4">
          <p class="text-sm text-gray-600 mb-3">Or enter your email for quick access:</p>
          <form action="/api/portal-redirect" method="get" class="flex gap-2">
            <input 
              type="email" 
              name="email" 
              placeholder="<EMAIL>"
              class="flex-1 px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              required
            />
            <button 
              type="submit"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
            >
              Access
            </button>
          </form>
        </div>
      )}
    </div>
  )}

  {variant === 'button' && (
    <a 
      href="/api/portal-redirect" 
      class="inline-flex items-center justify-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-full font-semibold transition-all duration-200 hover:bg-blue-700 hover:shadow-lg hover:-translate-y-0.5"
    >
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
      My Orders
    </a>
  )}

  {variant === 'link' && (
    <a 
      href="/api/portal-redirect" 
      class="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium transition-colors"
    >
      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
      View Order History
    </a>
  )}
</div>
